<script lang="ts">
	import ForwardIcon from "@/assets/svg/forward-icon.svelte";
	import LoopIcon from "@/assets/svg/loop-icon.svelte";
	import MinusIcon from "@/assets/svg/minus-icon.svelte";
	import MutedIcon from "@/assets/svg/muted-icon.svelte";
	import NextIcon from "@/assets/svg/next-icon.svelte";
	import PauseIcon from "@/assets/svg/pause-icon.svelte";
	import PlayIcon from "@/assets/svg/play-icon.svelte";
	import PlusIcon from "@/assets/svg/plus-icon.svelte";
	import PrevIcon from "@/assets/svg/prev-icon.svelte";
	import RewindIcon from "@/assets/svg/rewind-icon.svelte";
	import ShuffleIcon from "@/assets/svg/shuffle-icon.svelte";
	import VolumeIcon from "@/assets/svg/volume-icon.svelte";
	import type { ITab } from "@/interfaces/tab";
	import { YtEvents, dispatchYtEvent, seekVideoYt, setYtVolume, shuffleYt, toggleLoopYt, type SeekVideoYtArgs, type SetYtVolumeArgs, type ToggleLoopYtArgs } from "@/utils/yt-utils";
	import Button from "./button.svelte";
	import ArrowRight from "./icons/arrow-right.svelte";
	import VideoPlaylist from "./video-playlist.svelte";
	import { secondToTimeString } from "@/utils/second-to-time-string";

	export let tab: ITab;
	export let tabs: ITab[];
	export let idx: number;

	let showPlaylist = idx === 0 && tab.isYoutube && tab.isPlaylist;
	let youtubeVolume = tab.volume;
	let seekPosition = tab.duration === 0 ? 0 : (tab.currentTime / tab.duration) * 100;

	const toggleMute = async (tabId: number, state: boolean) => {
		await chrome.tabs.update(tabId, { muted: !state });
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isMuted = !state;
			}
			return tab;
		});
	};

	const refreshTabs = () => {
		setTimeout(async () => {
			const newTab = await chrome.tabs.get(tab.id);
			tabs = tabs.map((el) => {
				if (el.id === tab.id) el.title = newTab.title ?? "";
				return el;
			});
		}, 2000);
	};

	const setVolume = async () => {
		await chrome.scripting.executeScript<SetYtVolumeArgs, number>({
			func: setYtVolume,
			target: { tabId: tab.id },
			world: "MAIN",
			args: [youtubeVolume],
		});
	};

	const next = async (tabId: number) => {
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [YtEvents.next()],
		});

		refreshTabs();
	};

	const prev = async (tabId: number) => {
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [YtEvents.prev()],
		});

		refreshTabs();
	};

	const togglePlay = async (tabId: number) => {
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isPaused = !tab.isPaused;
			}
			return tab;
		});
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [tab.isYoutubeMusic ? YtEvents.playToggleMusic() : YtEvents.playToggle()],
		});
	};

	const toggleLoop = async (tabId: number) => {
		const res = await chrome.scripting.executeScript<ToggleLoopYtArgs, { loopState: number }>({
			func: toggleLoopYt,
			target: { tabId },
			world: "MAIN",
			args: [{ isPlaylist: tab.isPlaylist, isYoutubeMusic: !!tab.isYoutubeMusic }],
		});
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.loopState = res[0].result.loopState;
			}
			return tab;
		});
	};

	const toggleShuffle = async (tabId: number) => {
		if (tab.isYoutubeMusic) {
			await chrome.scripting.executeScript({
				func: dispatchYtEvent,
				target: { tabId },
				args: [YtEvents.shuffleMusic()],
			});
			return;
		}

		await chrome.scripting.executeScript({
			func: shuffleYt,
			target: { tabId },
			world: "MAIN",
		});
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isShuffled = !tab.isShuffled;
			}
			return tab;
		});
	};

	const seekVideo = async (tabId: number, second: number, to?: number) => {
		await chrome.scripting.executeScript<SeekVideoYtArgs, void>({
			func: seekVideoYt,
			target: { tabId },
			world: "MAIN",
			args: [{ second, to }],
		});
	};

	const openTab = async () => {
		await chrome.tabs.update(tab.id, {
			active: true,
		});
	};
</script>

<div class="flex w-full justify-between border border-gray-200 dark:border-gray-600 p-4 gap-3 rounded-xl flex-col
			 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700
			 shadow-lg hover:shadow-xl transition-all duration-200 relative">
	<button on:click={openTab} title="Open Tab"
			class="absolute top-3 right-3 fill-gray-600 dark:fill-orange-400 hover:fill-orange-500 dark:hover:fill-orange-300
				   p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200">
		<ArrowRight width={18} />
	</button>
	<div class="flex items-start gap-2 min-w-0 pr-10">
		{#if tab.iconUrl}
			<img width="18px" src={tab.iconUrl} alt="icon" />
		{/if}
		<span class="text-sm font-medium text-gray-800 dark:text-gray-200 break-words leading-relaxed" title={tab.title}>
			{tab.title}
		</span>
	</div>
	{#if tab.duration}
		<div class="flex items-center gap-2">
			<button
				title="Rewind 10s"
				class="neu-media-btn"
				on:click={() => seekVideo(tab.id, -10)}
			>
				<RewindIcon width={12} />
			</button>
			<span class="text-sm text-gray-700 dark:text-gray-300">{secondToTimeString(Math.floor((seekPosition / 100) * tab.duration))}</span>
			<input bind:value={seekPosition} type="range" min="0" max={100} class="flex-1" on:change={() => seekVideo(tab.id, 0, (seekPosition / 100) * tab.duration)} />
			<span class="text-sm text-gray-700 dark:text-gray-300">{secondToTimeString(Math.floor(tab.duration))}</span>
			<button
				title="Forward 10s"
				class="neu-media-btn"
				on:click={() => seekVideo(tab.id, 10)}
			>
				<ForwardIcon width={12} />
			</button>
		</div>
	{/if}
	<div class="flex gap-4">
		<button
			title="Mute/Unmute"
			on:click={() => toggleMute(tab.id, tab.isMuted)}
			class="neu-media-btn {tab.isMuted ? 'neu-media-btn-muted' : ''}"
		>
			{#if tab.isMuted}
				<MutedIcon width={12} />
			{:else}
				<VolumeIcon width={12} />
			{/if}
		</button>
		{#if tab.isYoutube}
			<div class="flex gap-2 items-center">
				<input bind:value={youtubeVolume} type="range" min="0" max="100" class="w-20" on:change={setVolume} />
				<span class="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900
							 text-blue-800 dark:text-blue-200 w-[35px] rounded-lg flex items-center justify-center h-full
							 shadow-sm border border-blue-200 dark:border-blue-700 font-semibold text-xs">{Math.floor(youtubeVolume)}</span>
			</div>
			<div class="flex items-center justify-center gap-2">
				<button
					title="Prev"
					class={`neu-media-btn ${!tab.isPlaylist ? "invisible" : "visible"}`}
					on:click={() => prev(tab.id)}
				>
					<PrevIcon width={12} />
				</button>
				<button
					title="Play/Pause"
					class="neu-media-btn neu-media-btn-primary"
					on:click={() => togglePlay(tab.id)}
				>
					{#if tab.isPaused}
						<PlayIcon width={12} />
					{:else}
						<PauseIcon width={12} />
					{/if}
				</button>
				<button
					title="Next"
					class="neu-media-btn"
					on:click={() => next(tab.id)}
				>
					<NextIcon width={12} />
				</button>
			</div>
			<div class="flex gap-2">
				<button
					title={`Loop ${tab.loopState ? "ON" : "OFF"}`}
					class={`neu-media-btn
							${!tab.isPlaylist || tab.isPlaylistMix
								? (!tab.loopState ? "neu-media-btn-inactive" : "neu-media-btn-active")
								: (!tab.loopState
									? "neu-media-btn-inactive"
									: tab.loopState === 1
										? "neu-media-btn-active"
										: "neu-media-btn-warning")}`}
					on:click={() => toggleLoop(tab.id)}
				>
					<LoopIcon width={12} />
				</button>
				{#if tab.isPlaylist && !tab.isPlaylistMix}
					<button
						title={`Shuffle ${tab.isShuffled ? "ON" : "OFF"}`}
						class={`neu-media-btn ${tab.isShuffled ? "neu-media-btn-active" : "neu-media-btn-inactive"}`}
						on:click={() => toggleShuffle(tab.id)}
					>
						<ShuffleIcon width={12} />
					</button>
				{/if}
			</div>
			{#if tab.isPlaylist}
				<div class="ml-auto">
					<button
						class={`neu-media-btn ${showPlaylist ? "" : "-rotate-90"}`}
						on:click={() => (showPlaylist = !showPlaylist)}
					>
						🔽
					</button>
				</div>
			{/if}
		{/if}
	</div>
	{#if showPlaylist}
		<VideoPlaylist {refreshTabs} tabId={tab.id} />
	{/if}
</div>

<style>
	/* 轻拟物风格媒体控制按钮 */
	:global(.neu-media-btn) {
		width: 2rem;
		height: 2rem;
		border-radius: 0.5rem;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 0.75rem;
		color: #4b5563;
		fill: #4b5563;
		background: linear-gradient(to bottom right, #e5e7eb, #d1d5db);
		transition: all 0.2s ease;
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1), -4px -4px 8px rgba(255, 255, 255, 0.8);
		border: none;
	}

	:global(.neu-media-btn:hover) {
		transform: translateY(-2px);
		box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.15), -6px -6px 12px rgba(255, 255, 255, 0.9);
	}

	:global(.neu-media-btn:active) {
		transform: translateY(0);
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.2), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* 主要播放按钮 - 橙色强调 */
	:global(.neu-media-btn-primary) {
		color: #ea580c;
		fill: #ea580c;
		background: linear-gradient(to bottom right, #fed7aa, #fdba74);
		box-shadow: 4px 4px 8px rgba(234, 88, 12, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
	}

	:global(.neu-media-btn-primary:hover) {
		box-shadow: 6px 6px 12px rgba(234, 88, 12, 0.25), -6px -6px 12px rgba(255, 255, 255, 0.9);
	}

	:global(.neu-media-btn-primary:active) {
		box-shadow: inset 2px 2px 4px rgba(234, 88, 12, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* 激活状态 - 绿色 */
	:global(.neu-media-btn-active) {
		color: #16a34a;
		fill: #16a34a;
		background: linear-gradient(to bottom right, #bbf7d0, #86efac);
		box-shadow: 4px 4px 8px rgba(22, 163, 74, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
	}

	:global(.neu-media-btn-active:hover) {
		box-shadow: 6px 6px 12px rgba(22, 163, 74, 0.25), -6px -6px 12px rgba(255, 255, 255, 0.9);
	}

	:global(.neu-media-btn-active:active) {
		box-shadow: inset 2px 2px 4px rgba(22, 163, 74, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* 非激活状态 - 红色 */
	:global(.neu-media-btn-inactive) {
		color: #dc2626;
		fill: #dc2626;
		background: linear-gradient(to bottom right, #fecaca, #fca5a5);
		box-shadow: 4px 4px 8px rgba(220, 38, 38, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
	}

	:global(.neu-media-btn-inactive:hover) {
		box-shadow: 6px 6px 12px rgba(220, 38, 38, 0.25), -6px -6px 12px rgba(255, 255, 255, 0.9);
	}

	:global(.neu-media-btn-inactive:active) {
		box-shadow: inset 2px 2px 4px rgba(220, 38, 38, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* 警告状态 - 橙色 */
	:global(.neu-media-btn-warning) {
		color: #ea580c;
		fill: #ea580c;
		background: linear-gradient(to bottom right, #fed7aa, #fdba74);
		box-shadow: 4px 4px 8px rgba(234, 88, 12, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
	}

	:global(.neu-media-btn-warning:hover) {
		box-shadow: 6px 6px 12px rgba(234, 88, 12, 0.25), -6px -6px 12px rgba(255, 255, 255, 0.9);
	}

	:global(.neu-media-btn-warning:active) {
		box-shadow: inset 2px 2px 4px rgba(234, 88, 12, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.8);
	}

	/* 静音状态特殊样式 */
	:global(.neu-media-btn-muted) {
		color: #dc2626;
		fill: #dc2626;
		background: linear-gradient(to bottom right, #fecaca, #fca5a5);
		box-shadow: 4px 4px 8px rgba(220, 38, 38, 0.2), -4px -4px 8px rgba(255, 255, 255, 0.8);
	}

	/* 深色模式 */
	:global(.dark) :global(.neu-media-btn) {
		color: #d1d5db;
		fill: #d1d5db;
		background: linear-gradient(to bottom right, #4b5563, #374151);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(255, 255, 255, 0.05);
	}

	:global(.dark) :global(.neu-media-btn:hover) {
		box-shadow: 6px 6px 12px rgba(0, 0, 0, 0.4), -6px -6px 12px rgba(255, 255, 255, 0.08);
	}

	:global(.dark) :global(.neu-media-btn:active) {
		box-shadow: inset 2px 2px 4px rgba(0, 0, 0, 0.3), inset -2px -2px 4px rgba(255, 255, 255, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-primary) {
		color: #fb923c;
		fill: #fb923c;
		background: linear-gradient(to bottom right, #7c2d12, #9a3412);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(251, 146, 60, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-active) {
		color: #4ade80;
		fill: #4ade80;
		background: linear-gradient(to bottom right, #14532d, #166534);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(74, 222, 128, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-inactive) {
		color: #f87171;
		fill: #f87171;
		background: linear-gradient(to bottom right, #7f1d1d, #991b1b);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(248, 113, 113, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-warning) {
		color: #fb923c;
		fill: #fb923c;
		background: linear-gradient(to bottom right, #7c2d12, #9a3412);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(251, 146, 60, 0.1);
	}

	:global(.dark) :global(.neu-media-btn-muted) {
		color: #f87171;
		fill: #f87171;
		background: linear-gradient(to bottom right, #7f1d1d, #991b1b);
		box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3), -4px -4px 8px rgba(248, 113, 113, 0.1);
	}
</style>